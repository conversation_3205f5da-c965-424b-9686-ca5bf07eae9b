import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useMultiAccount } from "../helpers/context/MultiAccountContext";
import { Box, Typography, Button } from "@mui/material";
import { Lock, ArrowBack } from "@mui/icons-material";

const ProtectedRoute = ({ children, requiredPermissions = [], fallbackRoute = "/dashboard" }) => {
  const { canAccessRoute, hasAnyPermission, permissionsLoaded, getPermissionNames } = useMultiAccount();
  const location = useLocation();

  // Wait for permissions to load
  if (!permissionsLoaded) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        flexDirection="column"
      >
        <Typography variant="h6" color="text.secondary">
          Loading permissions...
        </Typography>
      </Box>
    );
  }

  // Check route-based permissions first
  const currentRoute = location.pathname;
  const canAccessCurrentRoute = canAccessRoute(currentRoute);

  // Check specific required permissions if provided
  const hasRequiredPermissions = requiredPermissions.length === 0 || hasAnyPermission(requiredPermissions);

  // If user doesn't have access to the route or required permissions
  if (!canAccessCurrentRoute || !hasRequiredPermissions) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="400px"
        flexDirection="column"
        sx={{ p: 4, textAlign: "center" }}
      >
        <Lock sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
        <Typography variant="h4" gutterBottom color="text.primary">
          Access Restricted
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 500 }}>
          You don't have the required permissions to access this page. 
          Please contact your administrator if you believe this is an error.
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Your current permissions: {getPermissionNames().join(", ") || "None"}
        </Typography>
        <Button
          variant="contained"
          startIcon={<ArrowBack />}
          onClick={() => window.history.back()}
          sx={{ mr: 2 }}
        >
          Go Back
        </Button>
        <Button
          variant="outlined"
          onClick={() => (window.location.href = fallbackRoute)}
        >
          Go to Dashboard
        </Button>
      </Box>
    );
  }

  // User has access, render the protected content
  return children;
};

// Higher-order component for protecting routes with specific permissions
export const withPermissions = (Component, requiredPermissions = []) => {
  return (props) => (
    <ProtectedRoute requiredPermissions={requiredPermissions}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Component for checking feature-level permissions
export const PermissionGate = ({ 
  children, 
  permissions = [], 
  fallback = null, 
  requireAll = false 
}) => {
  const { hasAnyPermission, hasAllPermissions, permissionsLoaded } = useMultiAccount();

  if (!permissionsLoaded) {
    return fallback;
  }

  const hasAccess = requireAll 
    ? hasAllPermissions(permissions)
    : hasAnyPermission(permissions);

  return hasAccess ? children : fallback;
};

export default ProtectedRoute;
