import { createContext, useContext, useState, useEffect } from "react";
import { URL } from "../constant/Url";
import apiInstance from "../Axios/axiosINstance";
import siteConstant from "../constant/siteConstant";
import { fetchFromStorage, saveToStorage } from "./storage";

// Permission constants based on API response
export const PERMISSIONS = {
  POST: 1,
  MESSAGE: 2,
  ANALYTICS: 3,
  USER_MANAGEMENT: 4,
  BRAND_MANAGEMENT: 5,
  BLOCK_UNBLOCK: 6,
  FEEDBACK: 7,
};

// Permission names mapping
export const PERMISSION_NAMES = {
  [PERMISSIONS.POST]: "Post",
  [PERMISSIONS.MESSAGE]: "Message",
  [PERMISSIONS.ANALYTICS]: "Analytics",
  [PERMISSIONS.USER_MANAGEMENT]: "User_Management",
  [PERMISSIONS.BRAND_MANAGEMENT]: "Brand_Management",
  [PERMISSIONS.BLOCK_UNBLOCK]: "Block_Unblock",
  [PERMISSIONS.FEEDBACK]: "FeedBack",
};

// Route to permission mapping
export const ROUTE_PERMISSIONS = {
  "/dashboard": [PERMISSIONS.POST], // Dashboard might need post permission to show post-related content
  "/planner": [PERMISSIONS.POST],
  "/scheduled-post": [PERMISSIONS.POST],
  "/feed": [PERMISSIONS.POST],
  "/chat": [PERMISSIONS.MESSAGE],
  "/analytics": [PERMISSIONS.ANALYTICS],
  "/UserManagement": [PERMISSIONS.USER_MANAGEMENT],
  "/brands": [PERMISSIONS.BRAND_MANAGEMENT],
  "/feedback": [PERMISSIONS.FEEDBACK],
  "/role-manage": [PERMISSIONS.USER_MANAGEMENT],
  "/add-account": [PERMISSIONS.USER_MANAGEMENT],
};

// Feature to permission mapping for component-level restrictions
export const FEATURE_PERMISSIONS = {
  UPLOAD_POST: [PERMISSIONS.POST],
  VIEW_POSTS: [PERMISSIONS.POST],
  EDIT_POSTS: [PERMISSIONS.POST],
  DELETE_POSTS: [PERMISSIONS.POST],
  SEND_MESSAGE: [PERMISSIONS.MESSAGE],
  VIEW_MESSAGES: [PERMISSIONS.MESSAGE],
  VIEW_ANALYTICS: [PERMISSIONS.ANALYTICS],
  MANAGE_USERS: [PERMISSIONS.USER_MANAGEMENT],
  INVITE_USERS: [PERMISSIONS.USER_MANAGEMENT],
  BLOCK_USERS: [PERMISSIONS.BLOCK_UNBLOCK],
  UNBLOCK_USERS: [PERMISSIONS.BLOCK_UNBLOCK],
  MANAGE_BRANDS: [PERMISSIONS.BRAND_MANAGEMENT],
  CREATE_BRAND: [PERMISSIONS.BRAND_MANAGEMENT],
  EDIT_BRAND: [PERMISSIONS.BRAND_MANAGEMENT],
  DELETE_BRAND: [PERMISSIONS.BRAND_MANAGEMENT],
  SUBMIT_FEEDBACK: [PERMISSIONS.FEEDBACK],
  VIEW_FEEDBACK: [PERMISSIONS.FEEDBACK],
};

const MultiAccountContext = createContext();

// Global event emitter for account switch notifications
class AccountSwitchEventEmitter {
  constructor() {
    this.listeners = [];
  }

  subscribe(callback) {
    this.listeners.push(callback);
    return () => {
      this.listeners = this.listeners.filter(
        (listener) => listener !== callback
      );
    };
  }

  emit(accountData) {
    this.listeners.forEach((callback) => {
      try {
        callback(accountData);
      } catch (error) {
        console.error("Error in account switch listener:", error);
      }
    });
  }
}

export const accountSwitchEmitter = new AccountSwitchEventEmitter();

export const MultiAccountProvider = ({ children }) => {
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [storedAccounts, setStoredAccounts] = useState([]);
  const [loadingAccounts, setLoadingAccounts] = useState(true);
  const [accountsLoaded, setAccountsLoaded] = useState(false);
  const [userToken, setUserToken] = useState(null);
  const [switchingAccount, setSwitchingAccount] = useState(false);
  const [userPermissions, setUserPermissions] = useState([]);
  const [permissionsLoaded, setPermissionsLoaded] = useState(false);

  // Utility functions for permission checking
  const hasPermission = (permission) => {
    return userPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions) => {
    return permissions.some((permission) =>
      userPermissions.includes(permission)
    );
  };

  const hasAllPermissions = (permissions) => {
    return permissions.every((permission) =>
      userPermissions.includes(permission)
    );
  };

  const canAccessRoute = (route) => {
    const requiredPermissions = ROUTE_PERMISSIONS[route];
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // No permissions required for this route
    }
    return hasAnyPermission(requiredPermissions);
  };

  const canAccessFeature = (feature) => {
    const requiredPermissions = FEATURE_PERMISSIONS[feature];
    if (!requiredPermissions || requiredPermissions.length === 0) {
      return true; // No permissions required for this feature
    }
    return hasAnyPermission(requiredPermissions);
  };

  const getPermissionNames = () => {
    return userPermissions
      .map((permission) => PERMISSION_NAMES[permission])
      .filter(Boolean);
  };

  // Effect to track user token changes
  useEffect(() => {
    const checkToken = () => {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const token = userData?.token;

      // If token changes or is removed, reset account selection
      if (token !== userToken) {
        console.log("Token changed, resetting account selection");
        setUserToken(token);
        if (!token) {
          // User logged out - reset everything
          setSelectedAccount(null);
          setStoredAccounts([]);
          setAccountsLoaded(false);
          localStorage.removeItem("SelectedAccountId");
        } else {
          // User logged in - trigger account fetch
          setAccountsLoaded(false);
          setLoadingAccounts(true);
        }
      }
    };

    // Check immediately
    checkToken();

    // Set up interval to check for token changes
    const intervalId = setInterval(checkToken, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [userToken]);

  // Load stored accounts from localStorage
  useEffect(() => {
    const loadStoredAccounts = () => {
      try {
        const storedAccountsData = localStorage.getItem("storedAccounts");
        let accounts = [];

        if (storedAccountsData) {
          accounts = JSON.parse(storedAccountsData);
        } else {
          // Initialize with current user's account if no stored accounts exist
          const currentUserData = fetchFromStorage(
            siteConstant?.INDENTIFIERS?.USERDATA
          );
          if (currentUserData && currentUserData.token) {
            const currentAccount = {
              userId: currentUserData.user_id || currentUserData.id,
              name: currentUserData.name,
              username: currentUserData.username,
              profileImage: currentUserData.profile_image || "",
              token: currentUserData.token,
              email: currentUserData.email || "",
              brandId: currentUserData.brand_id || currentUserData.brandId || 1, // Use brand ID from user data
              isMainAccount: true, // Mark as main account
            };
            accounts = [currentAccount];
            localStorage.setItem("storedAccounts", JSON.stringify(accounts));
          }
        }

        setStoredAccounts(accounts);
        setAccountsLoaded(true);
      } catch (error) {
        console.error("Error loading stored accounts:", error);
      } finally {
        setLoadingAccounts(false);
      }
    };

    if (userToken) {
      loadStoredAccounts();
    }
  }, [userToken]);

  // Separate effect for account selection that only runs after accounts are loaded
  useEffect(() => {
    if (!accountsLoaded) {
      return;
    }

    console.log("Accounts loaded, selecting account...");
    console.log("Available accounts:", storedAccounts);
    console.log(
      "Stored SelectedAccountId in localStorage:",
      localStorage.getItem("SelectedAccountId")
    );

    const storedAccountId = localStorage.getItem("SelectedAccountId");
    if (!storedAccountId) {
      console.log("No stored account ID found");
      // Select first account by default for new users
      const firstAccount = storedAccounts[0];
      if (firstAccount) {
        console.log("Selecting first account:", firstAccount);
        setSelectedAccount(firstAccount);
        localStorage.setItem(
          "SelectedAccountId",
          firstAccount.userId.toString()
        );
      }
      return;
    }

    const accountId = parseInt(storedAccountId, 10);
    console.log("Looking for account with ID:", accountId);

    const accountToSelect = storedAccounts.find((a) => a.userId === accountId);
    console.log("Found account:", accountToSelect);

    if (accountToSelect) {
      console.log("Setting selected account to:", accountToSelect);
      setSelectedAccount(accountToSelect);
    } else {
      console.log("Stored account not found in available accounts");
      // Only fall back to first account if the stored account is not found
      const firstAccount = storedAccounts[0];
      console.log("Falling back to first account:", firstAccount);
      setSelectedAccount(firstAccount);
      if (firstAccount) {
        localStorage.setItem(
          "SelectedAccountId",
          firstAccount.userId.toString()
        );
      }
    }
  }, [accountsLoaded, storedAccounts, userToken]);

  // Effect to ensure current user is always in the stored accounts list
  useEffect(() => {
    if (!userToken || storedAccounts.length === 0) {
      return;
    }

    const currentUserData = fetchFromStorage(
      siteConstant?.INDENTIFIERS?.USERDATA
    );
    const currentUserId = currentUserData?.user_id || currentUserData?.id;

    // Check if current user is in stored accounts
    const currentUserInStored = storedAccounts.find(
      (account) => account.userId === currentUserId
    );

    if (!currentUserInStored && currentUserData) {
      // Add current user to stored accounts if not present
      const currentAccount = {
        userId: currentUserId,
        name: currentUserData.name,
        username: currentUserData.username,
        profileImage: currentUserData.profile_image || "",
        token: currentUserData.token,
        email: currentUserData.email || "",
        brandId: currentUserData.brand_id || currentUserData.brandId || 1, // Use brand ID from user data
        isMainAccount: true, // Mark as main account
      };

      const updatedAccounts = [...storedAccounts, currentAccount];
      setStoredAccounts(updatedAccounts);
      localStorage.setItem("storedAccounts", JSON.stringify(updatedAccounts));
    } else if (currentUserInStored && !currentUserInStored.isMainAccount) {
      // Ensure the current user is marked as main account
      const updatedAccounts = storedAccounts.map((account) =>
        account.userId === currentUserId
          ? { ...account, isMainAccount: true }
          : account
      );
      setStoredAccounts(updatedAccounts);
      localStorage.setItem("storedAccounts", JSON.stringify(updatedAccounts));
    }
  }, [userToken, storedAccounts]);

  // Load permissions from localStorage on app startup
  useEffect(() => {
    const loadStoredPermissions = () => {
      try {
        const storedPermissions = localStorage.getItem("userPermissions");
        if (storedPermissions) {
          const permissions = JSON.parse(storedPermissions);
          setUserPermissions(permissions);
          setPermissionsLoaded(true);
          console.log("Loaded permissions from storage:", permissions);
        }
      } catch (error) {
        console.error("Error loading stored permissions:", error);
      }
    };

    if (userToken) {
      loadStoredPermissions();
    }
  }, [userToken]);

  const handleAccountSelect = async (account) => {
    console.log("Handling account selection:", account);
    setSwitchingAccount(true);

    try {
      // Get the main account (initially logged-in user)
      const mainAccount = storedAccounts.find((acc) => acc.isMainAccount);
      const mainUserId = mainAccount?.userId;

      if (!mainUserId) {
        console.error("Main account not found");
        return;
      }

      // Call switch-user-account API with main user ID and target brand ID
      const switchResponse = await apiInstance.get(URL.SWITCH_USER_ACCOUNT, {
        headers: {
          user: mainUserId, // Main account's user ID
          brand: account?.brandId, // Brand ID of the account to switch to
        },
      });

      if (switchResponse.data?.status) {
        // Call test-permissions API
        const permissionsResponse = await apiInstance.get(
          URL.TEST_PERMISSIONS,
          {
            headers: {
              user: mainUserId, // Use the switched account's user ID for permissions
              brand: account?.brandId,
            },
          }
        );

        if (permissionsResponse.data?.status) {
          // Store permissions from API response
          const permissions = permissionsResponse.data?.data || [];
          setUserPermissions(permissions);
          setPermissionsLoaded(true);

          // Store permissions in localStorage for persistence
          localStorage.setItem("userPermissions", JSON.stringify(permissions));

          // Update selected account
          setSelectedAccount(account);
          localStorage.setItem(
            "SelectedAccountId",
            account?.userId?.toString()
          );

          // Update the current user data in storage with the selected account's data
          if (account) {
            const currentUserData = fetchFromStorage(
              siteConstant?.INDENTIFIERS?.USERDATA
            );
            const updatedUserData = {
              ...currentUserData,
              token: account.token,
              userId: account.userId,
              name: account.name,
              username: account.username,
              profile_image: account.profileImage,
            };
            saveToStorage(
              siteConstant?.INDENTIFIERS?.USERDATA,
              updatedUserData
            );
          }

          console.log(
            "Account switched successfully with permissions:",
            permissions
          );

          // Emit global account switch event to refresh all APIs
          accountSwitchEmitter.emit({
            account,
            permissions,
            timestamp: Date.now(),
            action: "ACCOUNT_SWITCHED",
          });
        } else {
          console.error("Permissions check failed");
        }
      } else {
        console.error("Account switch failed");
      }
    } catch (error) {
      console.error("Error switching account:", error);
    } finally {
      setSwitchingAccount(false);
    }
  };

  const addStoredAccount = (account) => {
    console.log("Adding stored account:", account);
    const newAccounts = [...storedAccounts, account];
    setStoredAccounts(newAccounts);
    localStorage.setItem("storedAccounts", JSON.stringify(newAccounts));
  };

  const removeStoredAccount = (accountId) => {
    console.log("Removing stored account:", accountId);
    const filteredAccounts = storedAccounts.filter(
      (account) => account.userId !== accountId
    );
    setStoredAccounts(filteredAccounts);
    localStorage.setItem("storedAccounts", JSON.stringify(filteredAccounts));

    // If the removed account was the selected one, select the first available account
    if (selectedAccount && selectedAccount.userId === accountId) {
      const firstAccount = filteredAccounts[0];
      if (firstAccount) {
        setSelectedAccount(firstAccount);
        localStorage.setItem(
          "SelectedAccountId",
          firstAccount.userId.toString()
        );
      } else {
        setSelectedAccount(null);
        localStorage.removeItem("SelectedAccountId");
      }
    }
  };

  const resetAccounts = () => {
    setSelectedAccount(null);
    setStoredAccounts([]);
    setUserPermissions([]);
    setPermissionsLoaded(false);
    localStorage.removeItem("SelectedAccountId");
    localStorage.removeItem("storedAccounts");
    localStorage.removeItem("userPermissions");
  };

  const getCurrentAccount = () => {
    return selectedAccount;
  };

  const getStoredAccounts = () => {
    return storedAccounts;
  };

  return (
    <MultiAccountContext.Provider
      value={{
        selectedAccount,
        handleAccountSelect,
        storedAccounts,
        addStoredAccount,
        removeStoredAccount,
        resetAccounts,
        loadingAccounts,
        switchingAccount,
        getCurrentAccount,
        getStoredAccounts,
        // Permission-related state and functions
        userPermissions,
        permissionsLoaded,
        hasPermission,
        hasAnyPermission,
        hasAllPermissions,
        canAccessRoute,
        canAccessFeature,
        getPermissionNames,
      }}
    >
      {children}
    </MultiAccountContext.Provider>
  );
};

export const useMultiAccount = () => useContext(MultiAccountContext);
